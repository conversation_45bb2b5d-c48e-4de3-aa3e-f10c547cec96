# API v2 Documentation - Bearer Authentication

This document describes the new API v2 endpoints that use Bearer token authentication.

## Authentication

All v2 endpoints require Bearer token authentication. Include the following header in your requests:

```
Authorization: Bearer tencent_health_api_v2_fixed_token_2024
```

## Endpoints

### 1. Get All Hospital Info
**Endpoint:** `POST /api/v2/getAllHospInfo`

**Description:** Retrieve basic information about all hospitals.

**Headers:**
```
Authorization: Bearer tencent_health_api_v2_fixed_token_2024
Content-Type: application/x-www-form-urlencoded
```

**Request Body:** None required

**Response:**
```json
{
    "code": 0,
    "message": "查询成功，共查询 1 条数据",
    "data": [
        {
            "hosName": "滇医通平台",
            "hosId": 800000089
        }
    ]
}
```

**cURL Example:**
```bash
curl -X POST http://localhost:5000/api/v2/getAllHospInfo \
  -H "Authorization: Bearer tencent_health_api_v2_fixed_token_2024" \
  -H "Content-Type: application/x-www-form-urlencoded"
```

### 2. Get Hospital Detail
**Endpoint:** `POST /api/v2/getHospDetail`

**Description:** Get detailed information about a specific hospital.

**Headers:**
```
Authorization: Bearer tencent_health_api_v2_fixed_token_2024
Content-Type: application/x-www-form-urlencoded
```

**Request Body:**
- `hosId` (required): Hospital ID

**Response:**
```json
{
    "code": 0,
    "message": "查询成功，共查询 N 条数据",
    "data": [
        {
            "hosName": "Hospital Name",
            "hosLevel": "三级甲等",
            "hosTelephone": "Phone Number",
            "hosImage": "Image URL",
            "hosId": "800000089",
            "branchId": "Branch ID",
            "hosIntro": "Hospital Introduction",
            "hosUrl": "",
            "hosAddress": "Hospital Address",
            "appId": "wx6fe267aca9b32a2f",
            "miniAppId": "",
            "originAppid": "",
            "location": "longitude,latitude",
            "isHTML": true
        }
    ]
}
```

**cURL Example:**
```bash
curl -X POST http://localhost:5000/api/v2/getHospDetail \
  -H "Authorization: Bearer tencent_health_api_v2_fixed_token_2024" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  --data "hosId=800000089"
```

### 3. Get Department List
**Endpoint:** `POST /api/v2/getDeptList`

**Description:** Get list of departments for a specific hospital branch.

**Headers:**
```
Authorization: Bearer tencent_health_api_v2_fixed_token_2024
Content-Type: application/x-www-form-urlencoded
```

**Request Body:**
- `hosId` (required): Hospital ID
- `branchId` (required): Branch ID

**Response:**
```json
{
    "code": 0,
    "message": "查询成功，共查询 N 条数据",
    "data": [
        {
            "deptId": "Department ID",
            "featureFlag": 0,
            "deptIntro": "Department Introduction",
            "deptName": "Department Name",
            "level": 2,
            "deptUrl": "Department URL",
            "deptMiniWxUrl": "",
            "deptMiniAlipayUrl": "",
            "deptLifeAlipayUrl": "",
            "deptEmbedAlipayUrl": "",
            "treatrange": "",
            "parentDeptId": "",
            "isHTML": false,
            "deptAddr": "Department Address"
        }
    ]
}
```

**cURL Example:**
```bash
curl -X POST http://localhost:5000/api/v2/getDeptList \
  -H "Authorization: Bearer tencent_health_api_v2_fixed_token_2024" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  --data "hosId=800000089&branchId=871054"
```

### 4. Get Doctor List
**Endpoint:** `POST /api/v2/getDoctorList`

**Description:** Get list of doctors for a specific department.

**Headers:**
```
Authorization: Bearer tencent_health_api_v2_fixed_token_2024
Content-Type: application/x-www-form-urlencoded
```

**Request Body:**
- `hosId` (required): Hospital ID
- `deptId` (required): Department ID
- `branchId` (optional): Branch ID

**Response:**
```json
{
    "code": 0,
    "message": "查询成功，共查询 N 条数据",
    "data": [
        {
            "doctorName": "Doctor Name",
            "doctorSex": -1,
            "doctorId": "Doctor ID",
            "doctorUrl": "Doctor URL",
            "doctorMiniWxUrl": "",
            "doctorMiniAlipayUrl": "",
            "doctorLifeAlipayUrl": "",
            "doctorEmbedAlipayUrl": "",
            "doctorJobTitle": "Job Title",
            "doctorIntro": "Doctor Introduction",
            "doctorGoodat": "Speciality",
            "doctorImage": "Doctor Image URL",
            "extraData": "{}",
            "isHTML": false
        }
    ]
}
```

**cURL Example:**
```bash
curl -X POST http://localhost:5000/api/v2/getDoctorList \
  -H "Authorization: Bearer tencent_health_api_v2_fixed_token_2024" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  --data "hosId=800000089&deptId=1&branchId=871054"
```

## Error Responses

### Authentication Errors

**Missing Authorization Header:**
```json
{
    "code": 401,
    "message": "Missing Authorization header"
}
```

**Invalid Authorization Format:**
```json
{
    "code": 401,
    "message": "Invalid Authorization header format. Expected 'Bearer <token>'"
}
```

**Invalid Bearer Token:**
```json
{
    "code": 401,
    "message": "Invalid bearer token"
}
```

### Parameter Errors

**Missing Required Parameters:**
```json
{
    "code": 400,
    "message": "Missing required parameter: hosId"
}
```

## Differences from v1 API

1. **Authentication Method:** v2 uses Bearer token authentication instead of HMAC signature
2. **URL Prefix:** All v2 endpoints are prefixed with `/api/v2/`
3. **Token:** Fixed token `tencent_health_api_v2_fixed_token_2024`
4. **Functionality:** Same business logic and data as v1 endpoints

## Testing

Use the provided `test_bearer_auth.py` script to test all endpoints:

```bash
python test_bearer_auth.py
```

Make sure your Flask application is running before executing the test script.
