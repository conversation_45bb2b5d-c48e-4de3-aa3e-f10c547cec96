# Bearer token authentication decorator
from functools import wraps

from flask import request, jsonify

from config import BEARER_TOKEN


def require_bearer_auth(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # Get Authorization header
        auth_header = request.headers.get('Authorization')
        
        # Check if Authorization header exists
        if not auth_header:
            return jsonify({"code": 401, "message": "Missing Authorization header"}), 401
        
        # Check if it's a Bearer token
        if not auth_header.startswith('Bearer '):
            return jsonify({"code": 401, "message": "Invalid Authorization header format. Expected 'Bearer <token>'"}), 401
        
        # Extract the token
        token = auth_header[7:]  # Remove 'Bearer ' prefix
        
        # Validate the token
        if token != BEARER_TOKEN:
            return jsonify({"code": 401, "message": "Invalid bearer token"}), 401
        
        return f(*args, **kwargs)
    
    return decorated_function
