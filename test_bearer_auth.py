#!/usr/bin/env python3
"""
Test script for the new Bearer authentication endpoints
"""

import requests
import json
from config import BEARER_TOKEN

# Base URL - adjust this to match your Flask app's URL
BASE_URL = "http://localhost:5000"

def test_bearer_auth_endpoints():
    """Test all new Bearer authentication endpoints"""
    
    # Headers with Bearer token
    headers = {
        "Authorization": f"Bearer {BEARER_TOKEN}",
        "Content-Type": "application/x-www-form-urlencoded"
    }
    
    # Test data
    test_data = {
        "hosId": "800000089",
        "branchId": "871054",
        "deptId": "1"
    }
    
    print("Testing Bearer Authentication Endpoints")
    print("=" * 50)
    
    # Test 1: getAllHospInfo
    print("\n1. Testing /api/v2/getAllHospInfo")
    try:
        response = requests.post(f"{BASE_URL}/api/v2/getAllHospInfo", headers=headers)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.json()}")
    except Exception as e:
        print(f"Error: {e}")
    
    # Test 2: getHospDetail
    print("\n2. Testing /api/v2/getHospDetail")
    try:
        response = requests.post(
            f"{BASE_URL}/api/v2/getHospDetail", 
            headers=headers,
            data={"hosId": test_data["hosId"]}
        )
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.json()}")
    except Exception as e:
        print(f"Error: {e}")
    
    # Test 3: getDeptList
    print("\n3. Testing /api/v2/getDeptList")
    try:
        response = requests.post(
            f"{BASE_URL}/api/v2/getDeptList", 
            headers=headers,
            data={
                "hosId": test_data["hosId"],
                "branchId": test_data["branchId"]
            }
        )
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.json()}")
    except Exception as e:
        print(f"Error: {e}")
    
    # Test 4: getDoctorList
    print("\n4. Testing /api/v2/getDoctorList")
    try:
        response = requests.post(
            f"{BASE_URL}/api/v2/getDoctorList", 
            headers=headers,
            data={
                "hosId": test_data["hosId"],
                "branchId": test_data["branchId"],
                "deptId": test_data["deptId"]
            }
        )
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.json()}")
    except Exception as e:
        print(f"Error: {e}")
    
    # Test 5: Invalid token
    print("\n5. Testing with invalid Bearer token")
    invalid_headers = {
        "Authorization": "Bearer invalid_token",
        "Content-Type": "application/x-www-form-urlencoded"
    }
    try:
        response = requests.post(f"{BASE_URL}/api/v2/getAllHospInfo", headers=invalid_headers)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.json()}")
    except Exception as e:
        print(f"Error: {e}")
    
    # Test 6: Missing Authorization header
    print("\n6. Testing without Authorization header")
    no_auth_headers = {
        "Content-Type": "application/x-www-form-urlencoded"
    }
    try:
        response = requests.post(f"{BASE_URL}/api/v2/getAllHospInfo", headers=no_auth_headers)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.json()}")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    print("Make sure your Flask app is running on http://localhost:5000")
    print("You can start it with: python app.py")
    print()
    test_bearer_auth_endpoints()
