from flask import Flask, request, jsonify

from authentication_decorator import require_auth
from bearer_auth_decorator import require_bearer_auth
from static import service

app = Flask(__name__)


@app.route('/getAllHospInfo', methods=['POST'])
@require_auth
def get_all_hosp_info():
    hospitals = [{"hosName": "滇医通平台", "hosId": 800000089}, ]

    return jsonify({
        "code": 0,
        "message": f"查询成功，共查询 {len(hospitals)} 条数据",
        "data": hospitals
    })


@app.route('/getHospDetail', methods=['POST'])
@require_auth
def get_hosp_detail():
    """Get detailed hospital information"""
    hos_id = request.form.get('hosId')

    if not hos_id:
        return jsonify({"code": 400, "message": "Missing required parameter: hosId"}), 400

    # Get main hospital
    hospitals = service.get_tencent_hospital_detail()
    if hospitals is None:
        hospitals = []

    return jsonify({
        "code": 0,
        "message": f"查询成功，共查询 {len(hospitals)} 条数据",
        "data": list(hospitals)
    })


@app.route('/getDeptList', methods=['POST'])
@require_auth
def get_dept_list():
    """Get department list for a hospital"""
    hos_id = request.form.get('hosId')
    branch_id = request.form.get('branchId')

    if not hos_id:
        return jsonify({"code": 400, "message": "Missing required parameter: hosId"}), 400

    if not branch_id:
        return jsonify({"code": 400, "message": "Missing required parameter: branchId"}), 400

    # Get departments
    departments = service.get_tencent_departments(branch_id)
    if departments is None:
        departments = []

    # In a real implementation, we would filter departments by hospital and branch
    # For this example, we'll just return all departments
    return jsonify({
        "code": 0,
        "message": f"查询成功，共查询 {len(departments)} 条数据",
        "data": list(departments)
    })


@app.route('/getDoctorList', methods=['POST'])
@require_auth
def get_doctor_list():
    """Get doctor list for a department"""
    dept_id = request.form.get('deptId')
    hos_id = request.form.get('hosId')
    branch_id = request.form.get('branchId')

    if not dept_id or not hos_id:
        return jsonify({"code": 400, "message": "Missing required parameters"}), 400

    doctors = service.get_tencent_doctors(branch_id, dept_id)
    if doctors is None:
        doctors = []

    return jsonify({
        "code": 0,
        "message": f"查询成功，共查询 {len(doctors)} 条数据",
        "data": doctors
    })


if __name__ == '__main__':
    app.run()
